#!/usr/bin/env python3
"""
Prompt-RAG 實現
基於 GPT-4V 等模型的 Prompt-RAG 直接推理生成描述方法
"""

import json
import base64
import openai
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import cv2
import numpy as np
from PIL import Image
import io

from .knowledge_base import IPCKnowledgeBase

class PromptRAGGenerator:
    """Prompt-RAG 生成器"""
    
    def __init__(self, api_key: str = None, model: str = "gpt-4-vision-preview"):
        """初始化 Prompt-RAG 生成器"""
        self.api_key = api_key
        self.model = model
        if api_key:
            openai.api_key = api_key
        
        # 載入知識庫
        self.knowledge_base = IPCKnowledgeBase()
        self.knowledge_base.build_knowledge_vectors()
        
        # RAG 提示模板
        self.rag_prompts = {
            'system_prompt': """
你是一位專業的 PCB 品質檢測專家，具備豐富的電路板缺陷分析經驗。
你的任務是分析 PCB 影像並生成詳細的缺陷描述報告。

請根據以下知識庫資訊和影像內容，生成準確、專業的缺陷描述：

知識庫資訊：
{knowledge_context}

請按照以下格式生成描述：
1. 缺陷類型識別
2. 位置描述
3. 尺寸估算
4. 嚴重程度評估
5. 可能成因分析
6. 影響評估
7. IPC 標準參考
8. 建議處理方式
""",
            
            'user_prompt': """
請分析這張 PCB 影像，識別其中的缺陷並生成詳細的檢測報告。

影像檔名：{filename}
預期缺陷類型：{expected_defect_type}

請提供：
1. 詳細的缺陷描述
2. 根據 IPC 標準的品質評估
3. 專業的技術建議
"""
        }
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """將影像編碼為 base64 格式"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"影像編碼錯誤: {e}")
            return ""
    
    def extract_defect_type_from_filename(self, filename: str) -> str:
        """從檔案名稱提取缺陷類型"""
        filename_lower = filename.lower()
        defect_types = {
            'missing_hole': '缺孔',
            'mouse_bite': '鼠咬',
            'open_circuit': '開路',
            'short': '短路',
            'spur': '毛刺',
            'spurious_copper': '假銅'
        }
        
        for defect_key, defect_name in defect_types.items():
            if defect_key in filename_lower:
                return defect_key
        
        return 'unknown'
    
    def retrieve_relevant_knowledge(self, defect_type: str, filename: str) -> str:
        """檢索相關知識"""
        # 構建查詢文本
        query = f"{defect_type} PCB 缺陷 品質檢測"
        
        # 檢索相關知識
        knowledge_results = self.knowledge_base.retrieve_relevant_knowledge(query, top_k=3)
        
        # 獲取 IPC 標準
        ipc_standard = self.knowledge_base.get_ipc_standard_for_defect(defect_type)
        
        # 獲取品質門檻
        quality_threshold = self.knowledge_base.get_quality_threshold(defect_type)
        
        # 組織知識上下文
        knowledge_context = []
        
        # 添加 IPC 標準資訊
        if ipc_standard:
            knowledge_context.append(f"IPC 標準: {ipc_standard['standard']} {ipc_standard['section']}")
            knowledge_context.append(f"標準描述: {ipc_standard['description']}")
            knowledge_context.append(f"品質要求: {ipc_standard['requirement']}")
        
        # 添加品質門檻
        if quality_threshold:
            threshold_info = ", ".join([f"{k}: {v}" for k, v in quality_threshold.items()])
            knowledge_context.append(f"品質門檻: {threshold_info}")
        
        # 添加檢索到的案例
        for result in knowledge_results:
            if result['metadata']['type'] == 'defect_case':
                case_data = result['metadata']['data']
                knowledge_context.append(f"案例參考: {case_data['description']}")
                knowledge_context.append(f"成因: {case_data['cause']}")
                knowledge_context.append(f"影響: {case_data['impact']}")
        
        return "\n".join(knowledge_context)
    
    def generate_description_with_vision(self, image_path: str) -> str:
        """使用視覺模型生成描述"""
        if not self.api_key:
            return self._fallback_description(image_path)
        
        try:
            # 編碼影像
            base64_image = self.encode_image_to_base64(image_path)
            if not base64_image:
                return self._fallback_description(image_path)
            
            # 提取缺陷類型
            filename = Path(image_path).name
            defect_type = self.extract_defect_type_from_filename(filename)
            
            # 檢索相關知識
            knowledge_context = self.retrieve_relevant_knowledge(defect_type, filename)
            
            # 構建提示
            system_prompt = self.rag_prompts['system_prompt'].format(
                knowledge_context=knowledge_context
            )
            
            user_prompt = self.rag_prompts['user_prompt'].format(
                filename=filename,
                expected_defect_type=defect_type
            )
            
            # 調用 GPT-4V
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": user_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=800,
                temperature=0.3
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"GPT-4V 生成失敗，使用備選方案: {e}")
            return self._fallback_description(image_path)
    
    def _fallback_description(self, image_path: str) -> str:
        """備選描述生成方法"""
        filename = Path(image_path).name
        defect_type = self.extract_defect_type_from_filename(filename)
        
        # 檢索相關知識
        knowledge_context = self.retrieve_relevant_knowledge(defect_type, filename)
        
        # 基於知識庫生成描述
        defect_names = {
            'missing_hole': '缺孔',
            'mouse_bite': '鼠咬',
            'open_circuit': '開路',
            'short': '短路',
            'spur': '毛刺',
            'spurious_copper': '假銅'
        }
        
        defect_zh = defect_names.get(defect_type, '未知缺陷')
        
        # 獲取 IPC 標準資訊
        ipc_standard = self.knowledge_base.get_ipc_standard_for_defect(defect_type)
        
        description = f"""
PCB 品質檢測報告

1. 缺陷類型識別：{defect_zh}
2. 影像檔案：{filename}
3. 檢測結果：在 PCB 影像中發現{defect_zh}缺陷

4. IPC 標準參考：
{ipc_standard.get('standard', 'N/A')} - {ipc_standard.get('title', 'N/A')}
{ipc_standard.get('description', 'N/A')}

5. 品質評估：
根據 IPC-A-610 Class 3 標準，此類缺陷{ipc_standard.get('requirement', '需要進一步評估')}

6. 相關知識：
{knowledge_context[:200]}...

7. 建議處理：
建議根據 IPC 標準進行詳細檢查，並採取相應的品質控制措施。
"""
        
        return description.strip()
    
    def batch_generate_descriptions(self, image_dir: str, output_file: str) -> List[Dict]:
        """批量生成描述"""
        image_dir = Path(image_dir)
        results = []
        
        # 支援的影像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        
        image_files = [f for f in image_dir.rglob('*') 
                      if f.suffix.lower() in image_extensions]
        
        total_count = len(image_files)
        print(f"開始批量生成 Prompt-RAG 描述，共 {total_count} 張影像")
        
        for i, image_path in enumerate(image_files):
            try:
                print(f"處理中 {i+1}/{total_count}: {image_path.name}")
                
                # 生成描述
                description = self.generate_description_with_vision(str(image_path))
                
                # 提取缺陷資訊
                defect_type = self.extract_defect_type_from_filename(image_path.name)
                
                result = {
                    'image_path': str(image_path.relative_to(image_dir)),
                    'filename': image_path.name,
                    'defect_type': defect_type,
                    'prompt_rag_description': description,
                    'generation_method': 'prompt_rag',
                    'model_used': self.model if self.api_key else 'fallback'
                }
                
                results.append(result)
                
            except Exception as e:
                print(f"處理 {image_path.name} 時發生錯誤: {e}")
                
                # 添加錯誤記錄
                result = {
                    'image_path': str(image_path.relative_to(image_dir)),
                    'filename': image_path.name,
                    'defect_type': 'error',
                    'prompt_rag_description': f"生成失敗: {str(e)}",
                    'generation_method': 'error',
                    'error': str(e)
                }
                results.append(result)
        
        # 儲存結果
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"Prompt-RAG 批量生成完成，結果儲存至: {output_path}")
        return results

def main():
    """主函數"""
    # 初始化 Prompt-RAG 生成器（不使用 API key 時會使用備選方案）
    generator = PromptRAGGenerator()
    
    # 設定路徑
    image_dir = "PCB_Datasets-main/Defect_Classification_Dataset"
    output_file = "data/processed/prompt_rag_descriptions.json"
    
    try:
        # 批量生成描述
        results = generator.batch_generate_descriptions(image_dir, output_file)
        print(f"Prompt-RAG 生成完成，共處理 {len(results)} 張影像")
        
    except Exception as e:
        print(f"Prompt-RAG 生成過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
