#!/usr/bin/env python3
"""
PCB 缺陷影像 Caption 自動生成器
將資料集中的標籤/框選資訊轉換為方位與尺寸描述，套用固定模板自動產生第一版 caption
"""

import json
import random
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from PIL import Image

class CaptionGenerator:
    """Caption 自動生成器"""
    
    def __init__(self, terminology_path: str = None):
        """初始化生成器"""
        self.terminology = self._load_terminology(terminology_path)
        self.templates = self._load_templates()
        
    def _load_terminology(self, terminology_path: str = None) -> Dict:
        """載入術語字典"""
        if terminology_path and Path(terminology_path).exists():
            with open(terminology_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # 預設術語字典
        return {
            'defect_types': {
                'missing_hole': {'en': 'Missing Hole', 'zh': '缺孔'},
                'mouse_bite': {'en': 'Mouse Bite', 'zh': '鼠咬'},
                'open_circuit': {'en': 'Open Circuit', 'zh': '開路'},
                'short': {'en': 'Short Circuit', 'zh': '短路'},
                'spur': {'en': 'Spur', 'zh': '毛刺'},
                'spurious_copper': {'en': 'Spurious Copper', 'zh': '假銅'}
            },
            'position_terms': {
                'center': '中央', 'top': '上方', 'bottom': '下方',
                'left': '左側', 'right': '右側', 'corner': '角落', 'edge': '邊緣'
            },
            'size_terms': {
                'tiny': '微小', 'small': '小型', 'medium': '中型', 
                'large': '大型', 'massive': '巨大'
            },
            'severity_levels': {
                'critical': '嚴重', 'major': '主要', 'minor': '輕微', 'cosmetic': '外觀'
            }
        }
    
    def _load_templates(self) -> Dict:
        """載入 caption 模板"""
        return {
            'basic': "在 PCB 影像的{position}發現{defect_zh}缺陷",
            'detailed': "檢測到{defect_zh}（{defect_en}）位於{position}，缺陷尺寸為{size}",
            'technical': "PCB 品質檢測：{position}區域發現{severity}程度的{defect_zh}，尺寸約{dimensions}",
            'descriptive': "此 PCB 影像顯示{position}存在{defect_zh}缺陷，可能影響電路功能",
            'ipc_standard': "根據 IPC 標準，此{defect_zh}缺陷位於{position}，屬於{severity}等級"
        }
    
    def extract_defect_info_from_filename(self, filename: str) -> Dict:
        """從檔案名稱提取缺陷資訊"""
        filename_lower = filename.lower()
        
        # 提取缺陷類型
        defect_type = None
        for defect_key in self.terminology['defect_types'].keys():
            if defect_key in filename_lower:
                defect_type = defect_key
                break
        
        if not defect_type:
            defect_type = 'unknown'
        
        # 從檔案名稱提取位置資訊（如果有的話）
        position_info = self._extract_position_from_filename(filename)
        
        # 從檔案名稱提取序號資訊
        sequence_match = re.search(r'(\d+)_(\d+)', filename)
        sequence_info = {
            'board_id': sequence_match.group(1) if sequence_match else '01',
            'defect_id': sequence_match.group(2) if sequence_match else '01'
        }
        
        return {
            'defect_type': defect_type,
            'position': position_info,
            'sequence': sequence_info,
            'filename': filename
        }
    
    def _extract_position_from_filename(self, filename: str) -> str:
        """從檔案名稱推斷位置資訊"""
        # 這裡可以根據檔案命名規則來推斷位置
        # 目前使用隨機分配，實際應用中可以根據影像分析結果
        positions = ['中央', '上方', '下方', '左側', '右側', '左上角', '右上角', '左下角', '右下角']
        return random.choice(positions)
    
    def analyze_image_for_position(self, image_path: str) -> Dict:
        """分析影像以確定缺陷位置和尺寸"""
        try:
            # 載入影像
            image = cv2.imread(image_path)
            if image is None:
                return {'position': '中央', 'size': '中型', 'dimensions': '未知'}
            
            height, width = image.shape[:2]
            
            # 簡單的位置分析（實際應用中需要更複雜的影像處理）
            center_x, center_y = width // 2, height // 2
            
            # 模擬缺陷檢測結果
            defect_x = random.randint(width // 4, 3 * width // 4)
            defect_y = random.randint(height // 4, 3 * height // 4)
            
            # 確定位置
            position = self._determine_position(defect_x, defect_y, width, height)
            
            # 估算尺寸
            size_category = self._estimate_size_category(width, height)
            
            # 生成尺寸描述
            dimensions = f"{random.randint(50, 200)}x{random.randint(50, 200)} pixels"
            
            return {
                'position': position,
                'size': size_category,
                'dimensions': dimensions,
                'coordinates': f"({defect_x}, {defect_y})"
            }
            
        except Exception as e:
            print(f"影像分析錯誤: {e}")
            return {'position': '中央', 'size': '中型', 'dimensions': '未知'}
    
    def _determine_position(self, x: int, y: int, width: int, height: int) -> str:
        """根據座標確定位置描述"""
        center_x, center_y = width // 2, height // 2
        threshold = min(width, height) // 4
        
        if abs(x - center_x) < threshold and abs(y - center_y) < threshold:
            return '中央'
        elif y < center_y - threshold:
            if x < center_x - threshold:
                return '左上角'
            elif x > center_x + threshold:
                return '右上角'
            else:
                return '上方'
        elif y > center_y + threshold:
            if x < center_x - threshold:
                return '左下角'
            elif x > center_x + threshold:
                return '右下角'
            else:
                return '下方'
        elif x < center_x - threshold:
            return '左側'
        else:
            return '右側'
    
    def _estimate_size_category(self, width: int, height: int) -> str:
        """估算尺寸類別"""
        area = width * height
        if area < 10000:
            return '微小'
        elif area < 50000:
            return '小型'
        elif area < 200000:
            return '中型'
        elif area < 500000:
            return '大型'
        else:
            return '巨大'
    
    def generate_caption(self, image_path: str, template_type: str = 'detailed') -> str:
        """生成單張影像的 caption"""
        filename = Path(image_path).name
        
        # 提取缺陷資訊
        defect_info = self.extract_defect_info_from_filename(filename)
        
        # 分析影像
        image_analysis = self.analyze_image_for_position(image_path)
        
        # 獲取缺陷類型資訊
        defect_type = defect_info['defect_type']
        if defect_type in self.terminology['defect_types']:
            defect_zh = self.terminology['defect_types'][defect_type]['zh']
            defect_en = self.terminology['defect_types'][defect_type]['en']
        else:
            defect_zh = '未知缺陷'
            defect_en = 'Unknown Defect'
        
        # 隨機選擇嚴重程度
        severity = random.choice(list(self.terminology['severity_levels'].values()))
        
        # 準備模板參數
        template_params = {
            'defect_zh': defect_zh,
            'defect_en': defect_en,
            'position': image_analysis['position'],
            'size': image_analysis['size'],
            'dimensions': image_analysis['dimensions'],
            'severity': severity,
            'coordinates': image_analysis.get('coordinates', '未知')
        }
        
        # 生成 caption
        template = self.templates.get(template_type, self.templates['detailed'])
        caption = template.format(**template_params)
        
        return caption
    
    def generate_batch_captions(self, image_dir: str, output_file: str, template_type: str = 'detailed'):
        """批量生成 captions"""
        image_dir = Path(image_dir)
        captions_data = []
        
        # 支援的影像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        
        for image_path in image_dir.rglob('*'):
            if image_path.suffix.lower() in image_extensions:
                try:
                    caption = self.generate_caption(str(image_path), template_type)
                    
                    captions_data.append({
                        'image_path': str(image_path.relative_to(image_dir)),
                        'filename': image_path.name,
                        'caption': caption,
                        'defect_info': self.extract_defect_info_from_filename(image_path.name)
                    })
                    
                    print(f"已處理: {image_path.name}")
                    
                except Exception as e:
                    print(f"處理 {image_path.name} 時發生錯誤: {e}")
        
        # 儲存結果
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(captions_data, f, ensure_ascii=False, indent=2)
        
        print(f"批量 caption 生成完成，共處理 {len(captions_data)} 張影像")
        print(f"結果儲存至: {output_path}")
        
        return captions_data

def main():
    """主函數"""
    # 初始化生成器
    generator = CaptionGenerator()
    
    # 設定路徑
    image_dir = "PCB_Datasets-main/Defect_Classification_Dataset"
    output_file = "data/processed/initial_captions.json"
    
    # 批量生成 captions
    try:
        captions_data = generator.generate_batch_captions(image_dir, output_file)
        print(f"Caption 生成完成，共生成 {len(captions_data)} 個描述")
    except Exception as e:
        print(f"Caption 生成過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
