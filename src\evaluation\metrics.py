#!/usr/bin/env python3
"""
評估驗證系統
建立自動指標（BLEU、ROUGE-L、CIDEr）與人工評估（正確性、專業度、可讀性）的雙軌驗證系統
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import jieba
from collections import Counter
import math
import re

class AutomaticMetrics:
    """自動評估指標"""
    
    def __init__(self):
        """初始化評估器"""
        pass
    
    def tokenize_chinese(self, text: str) -> List[str]:
        """中文分詞"""
        # 清理文本
        text = re.sub(r'[^\u4e00-\u9fff\w\s]', '', text)
        return list(jieba.cut(text))
    
    def calculate_bleu(self, reference: str, candidate: str, n: int = 4) -> float:
        """計算 BLEU 分數"""
        ref_tokens = self.tokenize_chinese(reference)
        cand_tokens = self.tokenize_chinese(candidate)
        
        if len(cand_tokens) == 0:
            return 0.0
        
        # 計算 n-gram 精確度
        precisions = []
        for i in range(1, n + 1):
            ref_ngrams = self._get_ngrams(ref_tokens, i)
            cand_ngrams = self._get_ngrams(cand_tokens, i)
            
            if len(cand_ngrams) == 0:
                precisions.append(0.0)
                continue
            
            # 計算匹配的 n-grams
            matches = 0
            for ngram in cand_ngrams:
                if ngram in ref_ngrams:
                    matches += min(cand_ngrams[ngram], ref_ngrams[ngram])
            
            precision = matches / sum(cand_ngrams.values())
            precisions.append(precision)
        
        # 計算幾何平均
        if any(p == 0 for p in precisions):
            return 0.0
        
        geo_mean = math.exp(sum(math.log(p) for p in precisions) / len(precisions))
        
        # 簡化的長度懲罰
        bp = min(1.0, len(cand_tokens) / max(1, len(ref_tokens)))
        
        return bp * geo_mean
    
    def _get_ngrams(self, tokens: List[str], n: int) -> Counter:
        """獲取 n-grams"""
        ngrams = Counter()
        for i in range(len(tokens) - n + 1):
            ngram = tuple(tokens[i:i + n])
            ngrams[ngram] += 1
        return ngrams
    
    def calculate_rouge_l(self, reference: str, candidate: str) -> float:
        """計算 ROUGE-L 分數"""
        ref_tokens = self.tokenize_chinese(reference)
        cand_tokens = self.tokenize_chinese(candidate)
        
        if len(ref_tokens) == 0 or len(cand_tokens) == 0:
            return 0.0
        
        # 計算最長公共子序列
        lcs_length = self._lcs_length(ref_tokens, cand_tokens)
        
        if lcs_length == 0:
            return 0.0
        
        # 計算 precision 和 recall
        precision = lcs_length / len(cand_tokens)
        recall = lcs_length / len(ref_tokens)
        
        # 計算 F1 分數
        if precision + recall == 0:
            return 0.0
        
        f1 = 2 * precision * recall / (precision + recall)
        return f1
    
    def _lcs_length(self, seq1: List[str], seq2: List[str]) -> int:
        """計算最長公共子序列長度"""
        m, n = len(seq1), len(seq2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if seq1[i - 1] == seq2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                else:
                    dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])
        
        return dp[m][n]
    
    def calculate_cider(self, reference: str, candidate: str) -> float:
        """計算 CIDEr 分數（簡化版）"""
        ref_tokens = self.tokenize_chinese(reference)
        cand_tokens = self.tokenize_chinese(candidate)
        
        if len(ref_tokens) == 0 or len(cand_tokens) == 0:
            return 0.0
        
        # 計算 TF-IDF 向量
        ref_tfidf = self._compute_tfidf(ref_tokens)
        cand_tfidf = self._compute_tfidf(cand_tokens)
        
        # 計算餘弦相似度
        similarity = self._cosine_similarity(ref_tfidf, cand_tfidf)
        return similarity
    
    def _compute_tfidf(self, tokens: List[str]) -> Dict[str, float]:
        """計算 TF-IDF"""
        tf = Counter(tokens)
        total_tokens = len(tokens)
        
        tfidf = {}
        for token, count in tf.items():
            tf_score = count / total_tokens
            # 簡化的 IDF（假設文檔集合大小）
            idf_score = math.log(1000 / (1 + count))
            tfidf[token] = tf_score * idf_score
        
        return tfidf
    
    def _cosine_similarity(self, vec1: Dict[str, float], vec2: Dict[str, float]) -> float:
        """計算餘弦相似度"""
        common_keys = set(vec1.keys()) & set(vec2.keys())
        
        if not common_keys:
            return 0.0
        
        dot_product = sum(vec1[key] * vec2[key] for key in common_keys)
        norm1 = math.sqrt(sum(val ** 2 for val in vec1.values()))
        norm2 = math.sqrt(sum(val ** 2 for val in vec2.values()))
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def evaluate_batch(self, references: List[str], candidates: List[str]) -> Dict[str, float]:
        """批量評估"""
        if len(references) != len(candidates):
            raise ValueError("參考文本和候選文本數量不匹配")
        
        bleu_scores = []
        rouge_scores = []
        cider_scores = []
        
        for ref, cand in zip(references, candidates):
            bleu_scores.append(self.calculate_bleu(ref, cand))
            rouge_scores.append(self.calculate_rouge_l(ref, cand))
            cider_scores.append(self.calculate_cider(ref, cand))
        
        return {
            'BLEU': np.mean(bleu_scores),
            'ROUGE-L': np.mean(rouge_scores),
            'CIDEr': np.mean(cider_scores),
            'BLEU_std': np.std(bleu_scores),
            'ROUGE-L_std': np.std(rouge_scores),
            'CIDEr_std': np.std(cider_scores)
        }

class HumanEvaluationFramework:
    """人工評估框架"""
    
    def __init__(self):
        """初始化人工評估框架"""
        self.evaluation_criteria = {
            'correctness': {
                'description': '描述的事實準確性',
                'scale': '1-5分（1=完全錯誤，5=完全正確）',
                'guidelines': [
                    '缺陷類型識別是否正確',
                    '位置描述是否準確',
                    '尺寸估算是否合理',
                    '技術術語使用是否正確'
                ]
            },
            'professionalism': {
                'description': '專業術語和技術深度',
                'scale': '1-5分（1=非常業餘，5=非常專業）',
                'guidelines': [
                    'IPC 標準引用是否恰當',
                    '技術術語使用是否專業',
                    '分析深度是否足夠',
                    '建議是否具有實用性'
                ]
            },
            'readability': {
                'description': '文本可讀性和流暢度',
                'scale': '1-5分（1=很難理解，5=非常清晰）',
                'guidelines': [
                    '語句是否通順',
                    '邏輯是否清晰',
                    '結構是否合理',
                    '表達是否簡潔明瞭'
                ]
            }
        }
    
    def create_evaluation_template(self, sample_data: List[Dict]) -> Dict:
        """建立評估模板"""
        template = {
            'evaluation_info': {
                'evaluator_name': '',
                'evaluation_date': '',
                'total_samples': len(sample_data),
                'criteria': self.evaluation_criteria
            },
            'samples': []
        }
        
        for i, sample in enumerate(sample_data):
            sample_template = {
                'sample_id': i + 1,
                'image_filename': sample.get('filename', ''),
                'defect_type': sample.get('defect_type', ''),
                'generated_description': sample.get('enhanced_caption', ''),
                'evaluation': {
                    'correctness': {
                        'score': 0,
                        'comments': ''
                    },
                    'professionalism': {
                        'score': 0,
                        'comments': ''
                    },
                    'readability': {
                        'score': 0,
                        'comments': ''
                    },
                    'overall_comments': '',
                    'suggested_improvements': ''
                }
            }
            template['samples'].append(sample_template)
        
        return template
    
    def analyze_human_evaluation(self, evaluation_data: Dict) -> Dict:
        """分析人工評估結果"""
        samples = evaluation_data['samples']
        
        # 收集分數
        correctness_scores = []
        professionalism_scores = []
        readability_scores = []
        
        for sample in samples:
            eval_data = sample['evaluation']
            correctness_scores.append(eval_data['correctness']['score'])
            professionalism_scores.append(eval_data['professionalism']['score'])
            readability_scores.append(eval_data['readability']['score'])
        
        # 計算統計數據
        analysis = {
            'correctness': {
                'mean': np.mean(correctness_scores),
                'std': np.std(correctness_scores),
                'min': np.min(correctness_scores),
                'max': np.max(correctness_scores)
            },
            'professionalism': {
                'mean': np.mean(professionalism_scores),
                'std': np.std(professionalism_scores),
                'min': np.min(professionalism_scores),
                'max': np.max(professionalism_scores)
            },
            'readability': {
                'mean': np.mean(readability_scores),
                'std': np.std(readability_scores),
                'min': np.min(readability_scores),
                'max': np.max(readability_scores)
            },
            'overall': {
                'mean': np.mean([
                    np.mean(correctness_scores),
                    np.mean(professionalism_scores),
                    np.mean(readability_scores)
                ])
            }
        }
        
        return analysis

class ComprehensiveEvaluator:
    """綜合評估器"""
    
    def __init__(self):
        """初始化綜合評估器"""
        self.auto_metrics = AutomaticMetrics()
        self.human_eval = HumanEvaluationFramework()
    
    def evaluate_model_performance(self, 
                                 generated_file: str,
                                 reference_file: str = None,
                                 output_dir: str = "data/evaluation") -> Dict:
        """評估模型性能"""
        
        # 載入生成的描述
        with open(generated_file, 'r', encoding='utf-8') as f:
            generated_data = json.load(f)
        
        results = {
            'automatic_metrics': {},
            'human_evaluation_template': {},
            'summary': {}
        }
        
        # 如果有參考文本，計算自動指標
        if reference_file and Path(reference_file).exists():
            with open(reference_file, 'r', encoding='utf-8') as f:
                reference_data = json.load(f)
            
            # 對齊數據
            references = []
            candidates = []
            
            for gen_item in generated_data:
                filename = gen_item['filename']
                # 尋找對應的參考文本
                ref_item = next((r for r in reference_data if r['filename'] == filename), None)
                if ref_item:
                    references.append(ref_item.get('caption', ''))
                    candidates.append(gen_item.get('enhanced_caption', ''))
            
            if references and candidates:
                auto_results = self.auto_metrics.evaluate_batch(references, candidates)
                results['automatic_metrics'] = auto_results
        
        # 建立人工評估模板
        human_template = self.human_eval.create_evaluation_template(generated_data[:20])  # 取前20個樣本
        results['human_evaluation_template'] = human_template
        
        # 儲存結果
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 儲存自動評估結果
        if results['automatic_metrics']:
            with open(output_path / 'automatic_metrics.json', 'w', encoding='utf-8') as f:
                json.dump(results['automatic_metrics'], f, ensure_ascii=False, indent=2)
        
        # 儲存人工評估模板
        with open(output_path / 'human_evaluation_template.json', 'w', encoding='utf-8') as f:
            json.dump(human_template, f, ensure_ascii=False, indent=2)
        
        # 生成摘要
        results['summary'] = {
            'total_samples': len(generated_data),
            'evaluation_date': str(Path(generated_file).stat().st_mtime),
            'automatic_metrics_available': bool(results['automatic_metrics']),
            'human_evaluation_samples': len(human_template['samples'])
        }
        
        print(f"評估結果已儲存至: {output_path}")
        return results

def main():
    """主函數"""
    # 初始化評估器
    evaluator = ComprehensiveEvaluator()
    
    # 設定檔案路徑
    generated_file = "data/processed/enhanced_captions.json"
    reference_file = "data/processed/gold_set.json"
    
    try:
        # 執行綜合評估
        results = evaluator.evaluate_model_performance(
            generated_file, reference_file
        )
        
        print("評估完成:")
        if results['automatic_metrics']:
            print(f"BLEU: {results['automatic_metrics']['BLEU']:.3f}")
            print(f"ROUGE-L: {results['automatic_metrics']['ROUGE-L']:.3f}")
            print(f"CIDEr: {results['automatic_metrics']['CIDEr']:.3f}")
        
        print(f"人工評估樣本數: {results['summary']['human_evaluation_samples']}")
        
    except Exception as e:
        print(f"評估過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
