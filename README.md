# PCB 缺陷影像→文字描述生成系統

本研究以「PCB 瑕疵影像→文字描述生成」為主軸，建立完整的自動化檢測報告生成系統。

## 🎯 研究目標

建立一個能夠自動分析 PCB 缺陷影像並生成專業檢測報告的系統，包含：
- 六類 PCB 缺陷的自動識別與描述
- 基於 IPC 標準的專業術語生成
- 檢索增強生成（RAG）技術應用
- 多模型比較與評估驗證

## 📋 系統架構

### 核心模組

1. **資料集整備與標準化** (`src/data_preparation/`)
   - PCB 缺陷資料集分析
   - 中英術語對照表建立
   - IPC 標準與允收準則整理

2. **Caption 自動生成系統** (`src/caption_generation/`)
   - 標籤/框選資訊轉換
   - 方位與尺寸描述生成
   - 固定模板自動產生

3. **語義增強模組** (`src/semantic_enhancement/`)
   - 大型語言模型語義增強
   - 成因與影響描述補充
   - Gold Set 高品質樣本製作

4. **檢索增強生成系統** (`src/rag_system/`)
   - IPC 標準知識庫建立
   - Prompt-RAG 實現
   - 知識檢索與增強

5. **評估驗證系統** (`src/evaluation/`)
   - 自動指標（BLEU、ROUGE-L、CIDEr）
   - 人工評估框架
   - 雙軌驗證機制

## 🚀 快速開始

### 環境設置

```bash
# 克隆專案
git clone <repository-url>
cd pcb-defect-caption-generation

# 安裝依賴
pip install -r requirements.txt

# 安裝中文分詞工具
python -c "import jieba; jieba.initialize()"
```

### 資料準備

確保 PCB 資料集位於正確位置：
```
PCB_Datasets-main/
├── Defect_Classification_Dataset/
│   ├── 01_missing_hole_01_1.jpg
│   ├── 01_mouse_bite_01_1.jpg
│   └── ...
└── README.md
```

### 執行完整流程

```bash
# 執行完整的研究流程
python main.py

# 或指定參數
python main.py --dataset PCB_Datasets-main/Defect_Classification_Dataset --output data
```

### 執行特定步驟

```bash
# 步驟1: 資料集分析
python main.py --step 1

# 步驟2: Caption 生成
python main.py --step 2

# 步驟3: 語義增強
python main.py --step 3

# 步驟4: Gold Set 製作
python main.py --step 4

# 步驟5: 知識庫建立
python main.py --step 5

# 步驟6: Prompt-RAG 生成
python main.py --step 6

# 步驟7: 評估驗證
python main.py --step 7
```

## 📊 支援的缺陷類型

| 缺陷類型 | 英文名稱 | 中文名稱 | IPC 標準 |
|---------|---------|---------|----------|
| missing_hole | Missing Hole | 缺孔 | IPC-A-610 Section 2.1 |
| mouse_bite | Mouse Bite | 鼠咬 | IPC-A-610 Section 2.2 |
| open_circuit | Open Circuit | 開路 | IPC-A-610 Section 2.3 |
| short | Short Circuit | 短路 | IPC-A-610 Section 2.4 |
| spur | Spur | 毛刺 | IPC-A-610 Section 2.5 |
| spurious_copper | Spurious Copper | 假銅 | IPC-A-610 Section 2.6 |

## 🔧 配置選項

### OpenAI API 配置（可選）

如果要使用 GPT-4V 進行高品質描述生成：

```bash
# 設定 API Key
export OPENAI_API_KEY="your-api-key-here"

# 或在執行時指定
python main.py --api-key your-api-key-here
```

### 自定義配置

修改 `main.py` 中的配置：

```python
config = {
    'dataset_path': 'your-dataset-path',
    'output_dir': 'your-output-dir',
    'sample_ratio_gold_set': 0.05,  # Gold Set 抽樣比例
    'enhancement_type': 'comprehensive',  # 語義增強類型
    'evaluation_samples': 20  # 人工評估樣本數
}
```

## 📁 輸出結構

執行完成後，會在輸出目錄生成以下結構：

```
data/
├── processed/
│   ├── dataset_analysis.json          # 資料集分析結果
│   ├── defect_statistics.csv          # 缺陷統計
│   ├── initial_captions.json          # 初始 captions
│   ├── enhanced_captions.json         # 語義增強後 captions
│   ├── gold_set.json                  # Gold Set 高品質樣本
│   └── prompt_rag_descriptions.json   # Prompt-RAG 生成結果
├── knowledge_base/
│   ├── ipc_knowledge_base.pkl         # IPC 知識庫
│   └── ipc_knowledge_base_vectorizer.pkl
├── evaluation/
│   ├── automatic_metrics.json         # 自動評估指標
│   └── human_evaluation_template.json # 人工評估模板
└── pipeline_results.json              # 完整流程結果
```

## 📈 評估指標

### 自動評估指標

- **BLEU**: 基於 n-gram 的文本相似度
- **ROUGE-L**: 基於最長公共子序列的評估
- **CIDEr**: 基於 TF-IDF 的語義相似度

### 人工評估維度

- **正確性** (1-5分): 描述的事實準確性
- **專業度** (1-5分): 專業術語和技術深度
- **可讀性** (1-5分): 文本流暢度和清晰度

## 🛠️ 開發與擴展

### 添加新的缺陷類型

1. 在 `dataset_analyzer.py` 中添加新的缺陷定義
2. 更新 `caption_generator.py` 中的模板
3. 在知識庫中添加相關 IPC 標準

### 自定義評估指標

在 `evaluation/metrics.py` 中添加新的評估函數：

```python
def custom_metric(reference: str, candidate: str) -> float:
    # 實現自定義評估邏輯
    return score
```

### 整合新的語言模型

在 `semantic_enhancement/` 目錄下添加新的增強器：

```python
class CustomEnhancer:
    def enhance_caption(self, caption: str) -> str:
        # 實現自定義增強邏輯
        return enhanced_caption
```

## 📝 研究成果

### 預期交付物

1. **經校驗的影像-文字資料集**
   - 包含 Gold Set 高品質樣本
   - 多層次的描述增強

2. **模型與推理程式碼**
   - 完整的 RAG 系統實現
   - 知識庫與配置檔案

3. **評估報告與文件**
   - 自動指標評估結果
   - 人工評估分析
   - 技術文件與使用說明

### 技術特色

- ✅ 純軟體實現，無需特殊硬體
- ✅ 模組化設計，易於擴展
- ✅ 支援中英雙語描述
- ✅ 基於 IPC 標準的專業術語
- ✅ 多模型比較與驗證
- ✅ 完整的評估體系

## 🤝 貢獻指南

歡迎提交 Issue 和 Pull Request 來改進系統！

## 📄 授權

本專案採用 MIT 授權條款。

## 📞 聯絡資訊

如有問題或建議，請聯絡專案維護者。

---

**注意**: 本系統為研究用途，實際生產環境使用前請進行充分測試和驗證。
