#!/usr/bin/env python3
"""
語義增強模組
使用大型語言模型對 caption 進行語義增強，保持事實不變的前提下補充成因與影響描述
"""

import json
import openai
import time
from pathlib import Path
from typing import Dict, List, Optional
import random

class LLMSemanticEnhancer:
    """使用 LLM 進行語義增強的類別"""
    
    def __init__(self, api_key: str = None, model: str = "gpt-3.5-turbo"):
        """初始化語義增強器"""
        self.api_key = api_key
        self.model = model
        if api_key:
            openai.api_key = api_key
        
        self.enhancement_prompts = {
            'cause_analysis': """
請為以下 PCB 缺陷描述補充可能的成因分析，保持原有事實描述不變：

原始描述：{original_caption}

請在保持原有描述準確性的前提下，補充：
1. 可能的成因
2. 對電路功能的潛在影響
3. 建議的處理方式

增強後的描述：
""",
            'technical_detail': """
請為以下 PCB 缺陷描述增加技術細節，使其更專業：

原始描述：{original_caption}

請補充：
1. 技術術語的使用
2. 相關的 IPC 標準參考
3. 品質等級評估

增強後的描述：
""",
            'comprehensive': """
請為以下 PCB 缺陷描述進行全面的語義增強：

原始描述：{original_caption}

請在保持事實準確的前提下，補充：
1. 缺陷的詳細特徵描述
2. 可能的形成原因
3. 對產品品質的影響
4. 相關的檢測標準
5. 建議的改善措施

增強後的描述：
"""
        }
    
    def enhance_caption_with_llm(self, original_caption: str, enhancement_type: str = 'comprehensive') -> str:
        """使用 LLM 增強單個 caption"""
        if not self.api_key:
            # 如果沒有 API key，使用規則式增強
            return self._rule_based_enhancement(original_caption, enhancement_type)
        
        try:
            prompt = self.enhancement_prompts[enhancement_type].format(
                original_caption=original_caption
            )
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一位 PCB 品質檢測專家，專門分析電路板缺陷。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            enhanced_caption = response.choices[0].message.content.strip()
            return enhanced_caption
            
        except Exception as e:
            print(f"LLM 增強失敗，使用規則式增強: {e}")
            return self._rule_based_enhancement(original_caption, enhancement_type)
    
    def _rule_based_enhancement(self, original_caption: str, enhancement_type: str) -> str:
        """規則式語義增強（當 LLM 不可用時的備選方案）"""
        
        # 缺陷成因資料庫
        defect_causes = {
            '缺孔': ['鑽孔機械故障', '鑽頭磨損', '定位偏移', '程式設定錯誤'],
            '鼠咬': ['蝕刻過度', '光阻膜品質不良', '曝光不均', '顯影液濃度異常'],
            '開路': ['蝕刻過度', '銅箔厚度不足', '機械損傷', '氧化腐蝕'],
            '短路': ['蝕刻不足', '銅箔殘留', '焊接橋接', '污染物質'],
            '毛刺': ['蝕刻參數不當', '光阻邊緣不清晰', '化學處理不完全'],
            '假銅': ['清潔不徹底', '蝕刻液失效', '遮罩不完整', '製程控制不當']
        }
        
        # 影響描述
        impact_descriptions = {
            '缺孔': '可能導致元件無法正確安裝，影響電氣連接',
            '鼠咬': '可能造成訊號完整性問題，增加電磁干擾風險',
            '開路': '將導致電路功能失效，無法形成完整的電流路徑',
            '短路': '可能造成電路異常工作，甚至損壞相關元件',
            '毛刺': '可能引起意外短路，影響電路可靠性',
            '假銅': '可能造成訊號干擾，影響電路性能'
        }
        
        # 檢測缺陷類型
        defect_type = None
        for defect in defect_causes.keys():
            if defect in original_caption:
                defect_type = defect
                break
        
        if not defect_type:
            return original_caption + "。此缺陷需要進一步分析以確定具體成因和影響。"
        
        # 隨機選擇成因和影響
        cause = random.choice(defect_causes[defect_type])
        impact = impact_descriptions[defect_type]
        
        # 根據增強類型生成不同的增強內容
        if enhancement_type == 'cause_analysis':
            enhanced = f"{original_caption}。此缺陷可能由{cause}所造成，{impact}。"
        
        elif enhancement_type == 'technical_detail':
            enhanced = f"{original_caption}。根據 IPC-A-610 標準，此類缺陷屬於不可接受等級，{impact}。建議檢查{cause}相關的製程參數。"
        
        else:  # comprehensive
            enhanced = f"{original_caption}。缺陷特徵分析：此{defect_type}缺陷可能由{cause}引起，{impact}。" \
                      f"根據 IPC 品質標準，建議立即檢查相關製程參數並進行改善。" \
                      f"此類缺陷若不及時處理，可能影響整體產品可靠性。"
        
        return enhanced
    
    def enhance_batch_captions(self, input_file: str, output_file: str, 
                             enhancement_type: str = 'comprehensive',
                             delay_seconds: float = 1.0) -> List[Dict]:
        """批量增強 captions"""
        
        # 載入原始 captions
        with open(input_file, 'r', encoding='utf-8') as f:
            captions_data = json.load(f)
        
        enhanced_data = []
        total_count = len(captions_data)
        
        print(f"開始批量語義增強，共 {total_count} 個 caption")
        
        for i, item in enumerate(captions_data):
            try:
                original_caption = item['caption']
                
                # 進行語義增強
                enhanced_caption = self.enhance_caption_with_llm(
                    original_caption, enhancement_type
                )
                
                # 建立增強後的資料項目
                enhanced_item = item.copy()
                enhanced_item.update({
                    'original_caption': original_caption,
                    'enhanced_caption': enhanced_caption,
                    'enhancement_type': enhancement_type,
                    'enhancement_timestamp': time.time()
                })
                
                enhanced_data.append(enhanced_item)
                
                print(f"已處理 {i+1}/{total_count}: {item['filename']}")
                
                # 避免 API 限制
                if self.api_key and delay_seconds > 0:
                    time.sleep(delay_seconds)
                
            except Exception as e:
                print(f"處理 {item['filename']} 時發生錯誤: {e}")
                # 保留原始資料
                enhanced_item = item.copy()
                enhanced_item.update({
                    'original_caption': item['caption'],
                    'enhanced_caption': item['caption'],  # 使用原始 caption
                    'enhancement_type': 'failed',
                    'error_message': str(e)
                })
                enhanced_data.append(enhanced_item)
        
        # 儲存增強後的結果
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data, f, ensure_ascii=False, indent=2)
        
        print(f"語義增強完成，結果儲存至: {output_path}")
        return enhanced_data
    
    def create_gold_set(self, enhanced_data: List[Dict], 
                       sample_ratio: float = 0.05,
                       output_file: str = None) -> List[Dict]:
        """建立 Gold Set（高品質樣本集）"""
        
        # 計算樣本數量
        total_count = len(enhanced_data)
        sample_count = max(1, int(total_count * sample_ratio))
        
        print(f"從 {total_count} 個樣本中抽取 {sample_count} 個建立 Gold Set")
        
        # 分層抽樣：確保每種缺陷類型都有代表
        defect_groups = {}
        for item in enhanced_data:
            defect_type = item['defect_info']['defect_type']
            if defect_type not in defect_groups:
                defect_groups[defect_type] = []
            defect_groups[defect_type].append(item)
        
        # 從每個缺陷類型中抽樣
        gold_set = []
        samples_per_type = max(1, sample_count // len(defect_groups))
        
        for defect_type, items in defect_groups.items():
            # 隨機抽樣
            sampled_items = random.sample(
                items, 
                min(samples_per_type, len(items))
            )
            
            # 標記為 Gold Set
            for item in sampled_items:
                gold_item = item.copy()
                gold_item['is_gold_set'] = True
                gold_item['gold_set_timestamp'] = time.time()
                gold_set.append(gold_item)
        
        # 如果還需要更多樣本，從剩餘的隨機抽取
        if len(gold_set) < sample_count:
            remaining_items = [item for item in enhanced_data 
                             if not any(g['filename'] == item['filename'] for g in gold_set)]
            additional_samples = random.sample(
                remaining_items,
                min(sample_count - len(gold_set), len(remaining_items))
            )
            
            for item in additional_samples:
                gold_item = item.copy()
                gold_item['is_gold_set'] = True
                gold_item['gold_set_timestamp'] = time.time()
                gold_set.append(gold_item)
        
        # 儲存 Gold Set
        if output_file:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(gold_set, f, ensure_ascii=False, indent=2)
            
            print(f"Gold Set 已儲存至: {output_path}")
        
        print(f"Gold Set 建立完成，共 {len(gold_set)} 個高品質樣本")
        return gold_set

def main():
    """主函數"""
    # 初始化語義增強器（不使用 API key 時會使用規則式增強）
    enhancer = LLMSemanticEnhancer()
    
    # 設定檔案路徑
    input_file = "data/processed/initial_captions.json"
    enhanced_output = "data/processed/enhanced_captions.json"
    gold_set_output = "data/processed/gold_set.json"
    
    try:
        # 批量語義增強
        enhanced_data = enhancer.enhance_batch_captions(
            input_file, enhanced_output, 'comprehensive'
        )
        
        # 建立 Gold Set
        gold_set = enhancer.create_gold_set(
            enhanced_data, sample_ratio=0.05, output_file=gold_set_output
        )
        
        print(f"語義增強流程完成")
        print(f"增強後資料: {len(enhanced_data)} 個")
        print(f"Gold Set: {len(gold_set)} 個")
        
    except Exception as e:
        print(f"語義增強過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
