#!/usr/bin/env python3
"""
檢索增強生成（RAG）系統 - 知識庫模組
建立以 IPC 門檻與案例為基礎的知識庫，實現檢索增強生成功能
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import pickle
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba

class IPCKnowledgeBase:
    """IPC 標準知識庫"""
    
    def __init__(self):
        """初始化知識庫"""
        self.ipc_standards = self._load_ipc_standards()
        self.defect_cases = self._load_defect_cases()
        self.quality_thresholds = self._load_quality_thresholds()
        
        # 文本向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=None,  # 中文需要自定義停用詞
            tokenizer=self._chinese_tokenizer
        )
        
        # 知識庫向量
        self.knowledge_vectors = None
        self.knowledge_texts = []
        self.knowledge_metadata = []
        
    def _chinese_tokenizer(self, text: str) -> List[str]:
        """中文分詞器"""
        return list(jieba.cut(text))
    
    def _load_ipc_standards(self) -> Dict:
        """載入 IPC 標準資料"""
        return {
            'IPC-A-610': {
                'title': 'Acceptability of Electronic Assemblies',
                'sections': {
                    '2.1': {
                        'title': 'Hole Requirements',
                        'description': '鑽孔品質要求與檢測標準',
                        'class_1': '基本功能要求',
                        'class_2': '專用服務要求',
                        'class_3': '高可靠性要求',
                        'defects': ['missing_hole', 'oversized_hole', 'undersized_hole']
                    },
                    '2.2': {
                        'title': 'Conductor Edge Roughness',
                        'description': '導體邊緣粗糙度標準',
                        'class_1': '允許輕微邊緣不規則',
                        'class_2': '邊緣應相對平滑',
                        'class_3': '邊緣必須平滑無缺陷',
                        'defects': ['mouse_bite', 'rough_edge']
                    },
                    '2.3': {
                        'title': 'Circuit Continuity',
                        'description': '電路連續性要求',
                        'class_1': '基本導通要求',
                        'class_2': '良好導通性能',
                        'class_3': '優異導通性能',
                        'defects': ['open_circuit', 'high_resistance']
                    },
                    '2.4': {
                        'title': 'Unwanted Connections',
                        'description': '意外連接控制標準',
                        'class_1': '無明顯短路',
                        'class_2': '嚴格控制短路',
                        'class_3': '零短路容忍',
                        'defects': ['short_circuit', 'bridging']
                    },
                    '2.5': {
                        'title': 'Conductor Protrusions',
                        'description': '導體突起控制',
                        'class_1': '允許小幅突起',
                        'class_2': '限制突起尺寸',
                        'class_3': '嚴格控制突起',
                        'defects': ['spur', 'protrusion']
                    },
                    '2.6': {
                        'title': 'Unwanted Copper',
                        'description': '不需要銅箔控制',
                        'class_1': '允許少量殘留',
                        'class_2': '限制殘留量',
                        'class_3': '嚴格清除殘留',
                        'defects': ['spurious_copper', 'copper_residue']
                    }
                }
            }
        }
    
    def _load_defect_cases(self) -> List[Dict]:
        """載入缺陷案例資料"""
        return [
            {
                'case_id': 'CASE_001',
                'defect_type': 'missing_hole',
                'description': '位於 PCB 中央區域的缺孔缺陷，直徑應為 0.8mm 但完全缺失',
                'cause': '鑽孔程式設定錯誤導致跳過此位置',
                'impact': '元件無法安裝，電路功能完全失效',
                'solution': '重新鑽孔或報廢處理',
                'ipc_class': 'Class 3 不可接受'
            },
            {
                'case_id': 'CASE_002',
                'defect_type': 'mouse_bite',
                'description': '線路邊緣出現鋸齒狀缺陷，深度約 0.1mm',
                'cause': '蝕刻過度或光阻膜品質不良',
                'impact': '可能影響訊號完整性，增加 EMI 風險',
                'solution': '檢查蝕刻參數，改善光阻品質',
                'ipc_class': 'Class 2 可接受，Class 3 不可接受'
            },
            {
                'case_id': 'CASE_003',
                'defect_type': 'open_circuit',
                'description': '走線中斷，導致電路開路',
                'cause': '蝕刻過度或機械損傷',
                'impact': '電路功能完全失效',
                'solution': '飛線修復或報廢',
                'ipc_class': '所有等級均不可接受'
            },
            {
                'case_id': 'CASE_004',
                'defect_type': 'short_circuit',
                'description': '相鄰走線間出現銅橋連接',
                'cause': '蝕刻不足或污染',
                'impact': '電路異常工作，可能損壞元件',
                'solution': '手工修復或重新製作',
                'ipc_class': '所有等級均不可接受'
            },
            {
                'case_id': 'CASE_005',
                'defect_type': 'spur',
                'description': '導線邊緣出現毛刺突起',
                'cause': '蝕刻參數不當',
                'impact': '可能造成短路風險',
                'solution': '手工修整或重新蝕刻',
                'ipc_class': 'Class 1 可接受，Class 2/3 需評估'
            },
            {
                'case_id': 'CASE_006',
                'defect_type': 'spurious_copper',
                'description': '不應存在區域出現銅箔殘留',
                'cause': '清潔不徹底或蝕刻液失效',
                'impact': '可能造成訊號干擾',
                'solution': '化學清除或機械修整',
                'ipc_class': 'Class 1 可接受，Class 2/3 不可接受'
            }
        ]
    
    def _load_quality_thresholds(self) -> Dict:
        """載入品質門檻標準"""
        return {
            'missing_hole': {
                'class_1': {'max_deviation': '20%', 'acceptance': 'conditional'},
                'class_2': {'max_deviation': '10%', 'acceptance': 'strict'},
                'class_3': {'max_deviation': '5%', 'acceptance': 'zero_tolerance'}
            },
            'mouse_bite': {
                'class_1': {'max_depth': '0.13mm', 'acceptance': 'acceptable'},
                'class_2': {'max_depth': '0.08mm', 'acceptance': 'conditional'},
                'class_3': {'max_depth': '0.05mm', 'acceptance': 'not_acceptable'}
            },
            'open_circuit': {
                'class_1': {'resistance': '>1MΩ', 'acceptance': 'not_acceptable'},
                'class_2': {'resistance': '>1MΩ', 'acceptance': 'not_acceptable'},
                'class_3': {'resistance': '>1MΩ', 'acceptance': 'not_acceptable'}
            },
            'short_circuit': {
                'class_1': {'resistance': '<100Ω', 'acceptance': 'not_acceptable'},
                'class_2': {'resistance': '<100Ω', 'acceptance': 'not_acceptable'},
                'class_3': {'resistance': '<100Ω', 'acceptance': 'not_acceptable'}
            },
            'spur': {
                'class_1': {'max_length': '0.13mm', 'acceptance': 'acceptable'},
                'class_2': {'max_length': '0.08mm', 'acceptance': 'conditional'},
                'class_3': {'max_length': '0.05mm', 'acceptance': 'not_acceptable'}
            },
            'spurious_copper': {
                'class_1': {'max_area': '0.13mm²', 'acceptance': 'acceptable'},
                'class_2': {'max_area': '0.08mm²', 'acceptance': 'conditional'},
                'class_3': {'max_area': '0.05mm²', 'acceptance': 'not_acceptable'}
            }
        }
    
    def build_knowledge_vectors(self):
        """建立知識庫向量索引"""
        print("建立知識庫向量索引...")
        
        # 收集所有知識文本
        self.knowledge_texts = []
        self.knowledge_metadata = []
        
        # 添加 IPC 標準文本
        for standard_name, standard_data in self.ipc_standards.items():
            for section_id, section_data in standard_data['sections'].items():
                text = f"{section_data['title']} {section_data['description']}"
                self.knowledge_texts.append(text)
                self.knowledge_metadata.append({
                    'type': 'ipc_standard',
                    'standard': standard_name,
                    'section': section_id,
                    'data': section_data
                })
        
        # 添加缺陷案例文本
        for case in self.defect_cases:
            text = f"{case['description']} {case['cause']} {case['impact']}"
            self.knowledge_texts.append(text)
            self.knowledge_metadata.append({
                'type': 'defect_case',
                'case_id': case['case_id'],
                'data': case
            })
        
        # 建立向量
        self.knowledge_vectors = self.vectorizer.fit_transform(self.knowledge_texts)
        print(f"知識庫向量索引建立完成，共 {len(self.knowledge_texts)} 個條目")
    
    def retrieve_relevant_knowledge(self, query: str, top_k: int = 3) -> List[Dict]:
        """檢索相關知識"""
        if self.knowledge_vectors is None:
            self.build_knowledge_vectors()
        
        # 向量化查詢
        query_vector = self.vectorizer.transform([query])
        
        # 計算相似度
        similarities = cosine_similarity(query_vector, self.knowledge_vectors).flatten()
        
        # 獲取最相關的 top_k 個結果
        top_indices = np.argsort(similarities)[-top_k:][::-1]
        
        results = []
        for idx in top_indices:
            if similarities[idx] > 0.1:  # 相似度閾值
                results.append({
                    'text': self.knowledge_texts[idx],
                    'metadata': self.knowledge_metadata[idx],
                    'similarity': float(similarities[idx])
                })
        
        return results
    
    def get_ipc_standard_for_defect(self, defect_type: str, quality_class: str = 'class_3') -> Dict:
        """獲取特定缺陷的 IPC 標準"""
        for standard_name, standard_data in self.ipc_standards.items():
            for section_id, section_data in standard_data['sections'].items():
                if defect_type in section_data.get('defects', []):
                    return {
                        'standard': standard_name,
                        'section': section_id,
                        'title': section_data['title'],
                        'description': section_data['description'],
                        'requirement': section_data.get(quality_class, '未定義')
                    }
        return {}
    
    def get_quality_threshold(self, defect_type: str, quality_class: str = 'class_3') -> Dict:
        """獲取品質門檻"""
        if defect_type in self.quality_thresholds:
            return self.quality_thresholds[defect_type].get(quality_class, {})
        return {}
    
    def save_knowledge_base(self, filepath: str):
        """儲存知識庫"""
        data = {
            'ipc_standards': self.ipc_standards,
            'defect_cases': self.defect_cases,
            'quality_thresholds': self.quality_thresholds,
            'knowledge_texts': self.knowledge_texts,
            'knowledge_metadata': self.knowledge_metadata
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(data, f)
        
        # 儲存向量化器
        vectorizer_path = filepath.replace('.pkl', '_vectorizer.pkl')
        with open(vectorizer_path, 'wb') as f:
            pickle.dump(self.vectorizer, f)
        
        print(f"知識庫已儲存至: {filepath}")
    
    def load_knowledge_base(self, filepath: str):
        """載入知識庫"""
        with open(filepath, 'rb') as f:
            data = pickle.load(f)
        
        self.ipc_standards = data['ipc_standards']
        self.defect_cases = data['defect_cases']
        self.quality_thresholds = data['quality_thresholds']
        self.knowledge_texts = data['knowledge_texts']
        self.knowledge_metadata = data['knowledge_metadata']
        
        # 載入向量化器
        vectorizer_path = filepath.replace('.pkl', '_vectorizer.pkl')
        with open(vectorizer_path, 'rb') as f:
            self.vectorizer = pickle.load(f)
        
        # 重建向量
        if self.knowledge_texts:
            self.knowledge_vectors = self.vectorizer.transform(self.knowledge_texts)
        
        print(f"知識庫已從 {filepath} 載入")

def main():
    """主函數"""
    # 建立知識庫
    kb = IPCKnowledgeBase()
    
    # 建立向量索引
    kb.build_knowledge_vectors()
    
    # 測試檢索
    test_query = "PCB 中央區域發現缺孔缺陷"
    results = kb.retrieve_relevant_knowledge(test_query)
    
    print(f"查詢: {test_query}")
    print("檢索結果:")
    for i, result in enumerate(results):
        print(f"{i+1}. 相似度: {result['similarity']:.3f}")
        print(f"   類型: {result['metadata']['type']}")
        print(f"   內容: {result['text'][:100]}...")
        print()
    
    # 儲存知識庫
    output_dir = Path("data/knowledge_base")
    output_dir.mkdir(parents=True, exist_ok=True)
    kb.save_knowledge_base(str(output_dir / "ipc_knowledge_base.pkl"))

if __name__ == "__main__":
    main()
