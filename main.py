#!/usr/bin/env python3
"""
PCB 缺陷影像→文字描述生成 主執行程式
整合所有模組，實現完整的研究流程
"""

import argparse
import json
import sys
from pathlib import Path
import time

# 添加 src 目錄到 Python 路徑
sys.path.append(str(Path(__file__).parent / "src"))

from data_preparation.dataset_analyzer import PCBDatasetAnalyzer
from caption_generation.caption_generator import CaptionGenerator
from semantic_enhancement.llm_enhancer import LLMSemanticEnhancer
from rag_system.knowledge_base import IPCKnowledgeBase
from rag_system.prompt_rag import PromptRAGGenerator
from evaluation.metrics import ComprehensiveEvaluator

class PCBDefectCaptionPipeline:
    """PCB 缺陷描述生成完整流程"""
    
    def __init__(self, config: dict = None):
        """初始化流程"""
        self.config = config or self._default_config()
        self.results = {}
        
    def _default_config(self) -> dict:
        """預設配置"""
        return {
            'dataset_path': 'PCB_Datasets-main/Defect_Classification_Dataset',
            'output_dir': 'data',
            'openai_api_key': None,  # 如果有 API key 可以在這裡設定
            'sample_ratio_gold_set': 0.05,
            'enhancement_type': 'comprehensive',
            'evaluation_samples': 20
        }
    
    def step1_dataset_analysis(self):
        """步驟1: 資料集整備與標準化"""
        print("=" * 60)
        print("步驟1: 資料集整備與標準化")
        print("=" * 60)
        
        analyzer = PCBDatasetAnalyzer(self.config['dataset_path'])
        
        # 執行分析
        output_dir = Path(self.config['output_dir']) / 'processed'
        result_path = analyzer.save_analysis_results(str(output_dir))
        
        self.results['dataset_analysis'] = {
            'output_path': str(result_path),
            'completed': True,
            'timestamp': time.time()
        }
        
        print(f"✓ 資料集分析完成，結果儲存至: {result_path}")
        return result_path
    
    def step2_caption_generation(self):
        """步驟2: Caption 自動生成"""
        print("\n" + "=" * 60)
        print("步驟2: Caption 自動生成系統")
        print("=" * 60)
        
        generator = CaptionGenerator()
        
        # 批量生成 captions
        output_file = Path(self.config['output_dir']) / 'processed' / 'initial_captions.json'
        captions_data = generator.generate_batch_captions(
            self.config['dataset_path'], 
            str(output_file)
        )
        
        self.results['caption_generation'] = {
            'output_file': str(output_file),
            'total_captions': len(captions_data),
            'completed': True,
            'timestamp': time.time()
        }
        
        print(f"✓ Caption 生成完成，共生成 {len(captions_data)} 個描述")
        return output_file
    
    def step3_semantic_enhancement(self):
        """步驟3: 語義增強"""
        print("\n" + "=" * 60)
        print("步驟3: 語義增強模組")
        print("=" * 60)
        
        enhancer = LLMSemanticEnhancer(
            api_key=self.config.get('openai_api_key'),
            model="gpt-3.5-turbo"
        )
        
        # 語義增強
        input_file = Path(self.config['output_dir']) / 'processed' / 'initial_captions.json'
        enhanced_output = Path(self.config['output_dir']) / 'processed' / 'enhanced_captions.json'
        
        enhanced_data = enhancer.enhance_batch_captions(
            str(input_file), 
            str(enhanced_output),
            self.config['enhancement_type']
        )
        
        self.results['semantic_enhancement'] = {
            'input_file': str(input_file),
            'output_file': str(enhanced_output),
            'total_enhanced': len(enhanced_data),
            'completed': True,
            'timestamp': time.time()
        }
        
        print(f"✓ 語義增強完成，共處理 {len(enhanced_data)} 個描述")
        return enhanced_output
    
    def step4_gold_set_creation(self):
        """步驟4: Gold Set 製作"""
        print("\n" + "=" * 60)
        print("步驟4: Gold Set 製作")
        print("=" * 60)
        
        enhancer = LLMSemanticEnhancer()
        
        # 載入增強後的資料
        enhanced_file = Path(self.config['output_dir']) / 'processed' / 'enhanced_captions.json'
        with open(enhanced_file, 'r', encoding='utf-8') as f:
            enhanced_data = json.load(f)
        
        # 建立 Gold Set
        gold_set_output = Path(self.config['output_dir']) / 'processed' / 'gold_set.json'
        gold_set = enhancer.create_gold_set(
            enhanced_data,
            sample_ratio=self.config['sample_ratio_gold_set'],
            output_file=str(gold_set_output)
        )
        
        self.results['gold_set_creation'] = {
            'output_file': str(gold_set_output),
            'total_samples': len(gold_set),
            'sample_ratio': self.config['sample_ratio_gold_set'],
            'completed': True,
            'timestamp': time.time()
        }
        
        print(f"✓ Gold Set 建立完成，共 {len(gold_set)} 個高品質樣本")
        return gold_set_output
    
    def step5_knowledge_base_setup(self):
        """步驟5: 檢索增強模組建立"""
        print("\n" + "=" * 60)
        print("步驟5: 檢索增強模組建立")
        print("=" * 60)
        
        # 建立知識庫
        kb = IPCKnowledgeBase()
        kb.build_knowledge_vectors()
        
        # 儲存知識庫
        kb_output = Path(self.config['output_dir']) / 'knowledge_base' / 'ipc_knowledge_base.pkl'
        kb_output.parent.mkdir(parents=True, exist_ok=True)
        kb.save_knowledge_base(str(kb_output))
        
        self.results['knowledge_base_setup'] = {
            'output_file': str(kb_output),
            'completed': True,
            'timestamp': time.time()
        }
        
        print(f"✓ 知識庫建立完成，儲存至: {kb_output}")
        return kb_output
    
    def step6_prompt_rag_generation(self):
        """步驟6: Prompt-RAG 方法實現"""
        print("\n" + "=" * 60)
        print("步驟6: Prompt-RAG 方法實現")
        print("=" * 60)
        
        generator = PromptRAGGenerator(
            api_key=self.config.get('openai_api_key'),
            model="gpt-4-vision-preview"
        )
        
        # 生成 Prompt-RAG 描述
        output_file = Path(self.config['output_dir']) / 'processed' / 'prompt_rag_descriptions.json'
        results = generator.batch_generate_descriptions(
            self.config['dataset_path'],
            str(output_file)
        )
        
        self.results['prompt_rag_generation'] = {
            'output_file': str(output_file),
            'total_generated': len(results),
            'completed': True,
            'timestamp': time.time()
        }
        
        print(f"✓ Prompt-RAG 生成完成，共處理 {len(results)} 張影像")
        return output_file
    
    def step7_evaluation(self):
        """步驟7: 評估驗證系統"""
        print("\n" + "=" * 60)
        print("步驟7: 評估驗證系統")
        print("=" * 60)
        
        evaluator = ComprehensiveEvaluator()
        
        # 評估增強後的描述
        generated_file = Path(self.config['output_dir']) / 'processed' / 'enhanced_captions.json'
        reference_file = Path(self.config['output_dir']) / 'processed' / 'gold_set.json'
        evaluation_dir = Path(self.config['output_dir']) / 'evaluation'
        
        results = evaluator.evaluate_model_performance(
            str(generated_file),
            str(reference_file),
            str(evaluation_dir)
        )
        
        self.results['evaluation'] = {
            'evaluation_dir': str(evaluation_dir),
            'automatic_metrics': results.get('automatic_metrics', {}),
            'human_evaluation_samples': results['summary']['human_evaluation_samples'],
            'completed': True,
            'timestamp': time.time()
        }
        
        print(f"✓ 評估系統建立完成，結果儲存至: {evaluation_dir}")
        return evaluation_dir
    
    def run_complete_pipeline(self):
        """執行完整流程"""
        print("開始執行 PCB 缺陷影像→文字描述生成完整流程")
        print(f"資料集路徑: {self.config['dataset_path']}")
        print(f"輸出目錄: {self.config['output_dir']}")
        
        start_time = time.time()
        
        try:
            # 執行所有步驟
            self.step1_dataset_analysis()
            self.step2_caption_generation()
            self.step3_semantic_enhancement()
            self.step4_gold_set_creation()
            self.step5_knowledge_base_setup()
            self.step6_prompt_rag_generation()
            self.step7_evaluation()
            
            # 儲存完整結果
            self._save_pipeline_results()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            print("\n" + "=" * 60)
            print("🎉 完整流程執行完成！")
            print("=" * 60)
            print(f"總執行時間: {total_time:.2f} 秒")
            print(f"結果儲存目錄: {self.config['output_dir']}")
            
            self._print_summary()
            
        except Exception as e:
            print(f"\n❌ 流程執行失敗: {e}")
            raise
    
    def _save_pipeline_results(self):
        """儲存流程結果"""
        results_file = Path(self.config['output_dir']) / 'pipeline_results.json'
        
        pipeline_summary = {
            'config': self.config,
            'results': self.results,
            'completion_time': time.time(),
            'status': 'completed'
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(pipeline_summary, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 流程結果已儲存至: {results_file}")
    
    def _print_summary(self):
        """列印摘要"""
        print("\n📊 執行摘要:")
        print("-" * 40)
        
        for step_name, step_result in self.results.items():
            if step_result.get('completed'):
                print(f"✓ {step_name}")
                
                # 顯示關鍵數據
                if 'total_captions' in step_result:
                    print(f"  - 生成描述數量: {step_result['total_captions']}")
                elif 'total_enhanced' in step_result:
                    print(f"  - 增強描述數量: {step_result['total_enhanced']}")
                elif 'total_samples' in step_result:
                    print(f"  - Gold Set 樣本數: {step_result['total_samples']}")
                elif 'total_generated' in step_result:
                    print(f"  - Prompt-RAG 生成數: {step_result['total_generated']}")
                elif 'human_evaluation_samples' in step_result:
                    print(f"  - 評估樣本數: {step_result['human_evaluation_samples']}")

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='PCB 缺陷影像→文字描述生成系統')
    parser.add_argument('--dataset', default='PCB_Datasets-main/Defect_Classification_Dataset',
                       help='資料集路徑')
    parser.add_argument('--output', default='data',
                       help='輸出目錄')
    parser.add_argument('--api-key', default=None,
                       help='OpenAI API Key（可選）')
    parser.add_argument('--step', type=int, default=None,
                       help='執行特定步驟（1-7），不指定則執行完整流程')
    
    args = parser.parse_args()
    
    # 建立配置
    config = {
        'dataset_path': args.dataset,
        'output_dir': args.output,
        'openai_api_key': args.api_key,
        'sample_ratio_gold_set': 0.05,
        'enhancement_type': 'comprehensive',
        'evaluation_samples': 20
    }
    
    # 初始化流程
    pipeline = PCBDefectCaptionPipeline(config)
    
    try:
        if args.step:
            # 執行特定步驟
            step_methods = {
                1: pipeline.step1_dataset_analysis,
                2: pipeline.step2_caption_generation,
                3: pipeline.step3_semantic_enhancement,
                4: pipeline.step4_gold_set_creation,
                5: pipeline.step5_knowledge_base_setup,
                6: pipeline.step6_prompt_rag_generation,
                7: pipeline.step7_evaluation
            }
            
            if args.step in step_methods:
                step_methods[args.step]()
            else:
                print(f"錯誤: 步驟 {args.step} 不存在，請選擇 1-7")
        else:
            # 執行完整流程
            pipeline.run_complete_pipeline()
            
    except KeyboardInterrupt:
        print("\n⚠️  流程被用戶中斷")
    except Exception as e:
        print(f"\n❌ 執行失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
